"""
Minimal Field Alignment Test
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import os

# Set random seeds
torch.manual_seed(42)
np.random.seed(42)

class MNISTLoader:
    @staticmethod
    def load_idx_images(filename: str) -> np.ndarray:
        with open(filename, 'rb') as f:
            magic = int.from_bytes(f.read(4), 'big')
            num_images = int.from_bytes(f.read(4), 'big')
            rows = int.from_bytes(f.read(4), 'big')
            cols = int.from_bytes(f.read(4), 'big')
            images = np.frombuffer(f.read(), dtype=np.uint8)
            images = images.reshape(num_images, rows, cols)
        return images.astype(np.float32) / 255.0

    @staticmethod
    def load_idx_labels(filename: str) -> np.ndarray:
        with open(filename, 'rb') as f:
            magic = int.from_bytes(f.read(4), 'big')
            num_labels = int.from_bytes(f.read(4), 'big')
            labels = np.frombuffer(f.read(), dtype=np.uint8)
        return labels

    @classmethod
    def load_mnist(cls, data_dir: str = "MNIST"):
        train_images = cls.load_idx_images(f"{data_dir}/train-images.idx3-ubyte")
        train_labels = cls.load_idx_labels(f"{data_dir}/train-labels.idx1-ubyte")
        test_images = cls.load_idx_images(f"{data_dir}/t10k-images.idx3-ubyte")
        test_labels = cls.load_idx_labels(f"{data_dir}/t10k-labels.idx1-ubyte")
        return train_images, train_labels, test_images, test_labels

class SimpleAutoencoder(nn.Module):
    def __init__(self, input_dim=784, latent_dim=2):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, latent_dim)
        )
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 128),
            nn.ReLU(),
            nn.Linear(128, input_dim),
            nn.Sigmoid()
        )
    
    def encode(self, x):
        return self.encoder(x)
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        z = self.encode(x)
        x_recon = self.decode(z)
        return x_recon, z

def compute_fields(model, x_original, z):
    """Compute distortion and complexity fields"""
    model.eval()
    
    # Ensure z requires gradients
    if not z.requires_grad:
        z.requires_grad_(True)
    
    # Distortion field
    x_recon = model.decode(z)
    x_orig_detached = x_original.detach()
    distortion = torch.sum((x_orig_detached - x_recon) ** 2)
    distortion_grad = torch.autograd.grad(distortion, z, create_graph=True, retain_graph=True)[0]
    
    # Complexity field (simplified)
    output_sum = torch.sum(x_recon)
    first_grad = torch.autograd.grad(output_sum, z, create_graph=True, retain_graph=True)[0]
    complexity = torch.sum(first_grad ** 2)
    complexity_grad = torch.autograd.grad(complexity, z, create_graph=True, retain_graph=True)[0]
    
    return distortion, distortion_grad, complexity, complexity_grad

def main():
    print("Minimal Field Alignment Test")
    print("="*50)
    
    # Load data
    print("Loading MNIST...")
    train_images, train_labels, test_images, test_labels = MNISTLoader.load_mnist()
    
    # Prepare data
    train_images = train_images.reshape(-1, 784)
    test_images = test_images.reshape(-1, 784)
    
    train_dataset = TensorDataset(torch.FloatTensor(train_images), torch.LongTensor(train_labels))
    test_dataset = TensorDataset(torch.FloatTensor(test_images), torch.LongTensor(test_labels))
    
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=256, shuffle=False)
    
    print(f"Train: {len(train_dataset)}, Test: {len(test_dataset)}")
    
    # Create model
    model = SimpleAutoencoder(latent_dim=2)
    optimizer = optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    print(f"Model: {model}")
    
    # Train for a few epochs
    print("\nTraining...")
    epochs = 5
    for epoch in range(epochs):
        model.train()
        train_loss = 0.0
        
        for batch_idx, (data, _) in enumerate(train_loader):
            optimizer.zero_grad()
            recon, _ = model(data)
            loss = criterion(recon, data)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
            
            if batch_idx > 10:  # Limit batches for testing
                break
        
        avg_loss = train_loss / min(11, len(train_loader))
        print(f"Epoch {epoch}: Loss = {avg_loss:.6f}")
    
    # Test field computation
    print("\nTesting field computation...")
    model.eval()
    
    # Get a test sample
    test_data, _ = next(iter(test_loader))
    x_sample = test_data[0:1]  # First sample
    
    # Encode
    with torch.no_grad():
        z_encoded = model.encode(x_sample)
    
    # Create z with gradients
    z = z_encoded.detach().clone()
    z.requires_grad_(True)
    
    try:
        distortion, distortion_grad, complexity, complexity_grad = compute_fields(model, x_sample, z)
        
        print(f"✅ Field computation successful!")
        print(f"Distortion: {distortion.item():.6f}")
        print(f"Complexity: {complexity.item():.6f}")
        print(f"Distortion grad norm: {torch.norm(distortion_grad).item():.6f}")
        print(f"Complexity grad norm: {torch.norm(complexity_grad).item():.6f}")
        
        # Compute alignment
        alignment = torch.dot(distortion_grad.flatten(), complexity_grad.flatten())
        print(f"Alignment (d⃗·c⃗): {alignment.item():.6f}")
        
        # Test on multiple samples
        print("\nTesting on multiple samples...")
        alignments = []
        
        for i in range(min(10, len(test_data))):
            x_sample = test_data[i:i+1]
            
            with torch.no_grad():
                z_encoded = model.encode(x_sample)
            
            z = z_encoded.detach().clone()
            z.requires_grad_(True)
            
            try:
                _, d_grad, _, c_grad = compute_fields(model, x_sample, z)
                alignment = torch.dot(d_grad.flatten(), c_grad.flatten())
                alignments.append(alignment.item())
            except Exception as e:
                print(f"Error on sample {i}: {e}")
        
        print(f"Mean alignment across {len(alignments)} samples: {np.mean(alignments):.6f}")
        print(f"Std alignment: {np.std(alignments):.6f}")
        
        if np.mean(alignments) < 0:
            print("✅ Negative alignment observed - potential field opposition!")
        else:
            print("⚠️ Positive alignment - no field opposition detected")
            
    except Exception as e:
        print(f"❌ Field computation failed: {e}")
        return False
    
    print("\n" + "="*50)
    print("Minimal test completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Tests failed!")
