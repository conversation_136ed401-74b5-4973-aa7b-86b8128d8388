"""
Comprehensive Field Alignment Validation Framework
Tested on a Simple Autoencoder with Greyscale MNIST

This experiment tests whether distortion and complexity vector fields in the latent space 
of an autoencoder exhibit the predicted anti-alignment ("optimal opposition") characteristic 
of compression-efficient learning.

Key Theory:
- Distortion field D(z): Points toward regions of high reconstruction error
- Complexity field C(z): Points toward regions requiring high model complexity
- Anti-alignment hypothesis: In well-regularized models, these fields should be 
  maximally opposed in high-magnitude regions (indicating optimal compression)
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
from sklearn.manifold import TSNE
import pandas as pd
import os
from typing import Dict, List, Tuple, Optional
import pickle
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class MNISTLoader:
    """Load MNIST dataset from IDX files"""
    
    @staticmethod
    def load_idx_images(filename: str) -> np.ndarray:
        """Load images from IDX3 format"""
        with open(filename, 'rb') as f:
            # Read header
            magic = int.from_bytes(f.read(4), 'big')
            num_images = int.from_bytes(f.read(4), 'big')
            rows = int.from_bytes(f.read(4), 'big')
            cols = int.from_bytes(f.read(4), 'big')
            
            # Read image data
            images = np.frombuffer(f.read(), dtype=np.uint8)
            images = images.reshape(num_images, rows, cols)
            
        return images.astype(np.float32) / 255.0  # Normalize to [0, 1]
    
    @staticmethod
    def load_idx_labels(filename: str) -> np.ndarray:
        """Load labels from IDX1 format"""
        with open(filename, 'rb') as f:
            # Read header
            magic = int.from_bytes(f.read(4), 'big')
            num_labels = int.from_bytes(f.read(4), 'big')
            
            # Read label data
            labels = np.frombuffer(f.read(), dtype=np.uint8)
            
        return labels
    
    @classmethod
    def load_mnist(cls, data_dir: str = "MNIST") -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load complete MNIST dataset"""
        train_images = cls.load_idx_images(f"{data_dir}/train-images.idx3-ubyte")
        train_labels = cls.load_idx_labels(f"{data_dir}/train-labels.idx1-ubyte")
        test_images = cls.load_idx_images(f"{data_dir}/t10k-images.idx3-ubyte")
        test_labels = cls.load_idx_labels(f"{data_dir}/t10k-labels.idx1-ubyte")

        return train_images, train_labels, test_images, test_labels

class SimpleAutoencoder(nn.Module):
    """Simple autoencoder with configurable latent dimension and regularization"""
    
    def __init__(self, input_dim: int = 784, latent_dim: int = 2, hidden_dims: List[int] = [256, 128]):
        super().__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        encoder_layers.append(nn.Linear(prev_dim, latent_dim))
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Decoder (symmetric to encoder)
        decoder_layers = []
        prev_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        decoder_layers.extend([
            nn.Linear(prev_dim, input_dim),
            nn.Sigmoid()  # Output in [0, 1] range
        ])
        self.decoder = nn.Sequential(*decoder_layers)
    
    def encode(self, x: torch.Tensor) -> torch.Tensor:
        """Encode input to latent space"""
        return self.encoder(x)
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """Decode from latent space"""
        return self.decoder(z)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning reconstruction and latent code"""
        z = self.encode(x)
        x_recon = self.decode(z)
        return x_recon, z

class FieldComputer:
    """Compute distortion and complexity fields in latent space"""
    
    def __init__(self, model: SimpleAutoencoder, device: torch.device):
        self.model = model
        self.device = device
        self.model.eval()
    
    def compute_distortion_field(self, x_original: torch.Tensor, z: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute distortion D(z) = ||x - x_hat||^2 and its gradient d_vec(z) = ∇_z D(z)

        Args:
            x_original: Original input image (no gradients needed)
            z: Latent code (requires_grad=True)

        Returns:
            distortion: Scalar distortion value
            distortion_grad: Gradient of distortion w.r.t. z
        """
        if not z.requires_grad:
            z.requires_grad_(True)

        # Decode z to get reconstruction
        x_recon = self.model.decode(z)

        # Compute distortion (MSE loss) - x_original should not require gradients
        x_orig_detached = x_original.detach()  # Ensure no gradients from x_original
        distortion = torch.sum((x_orig_detached - x_recon) ** 2)

        # Compute gradient w.r.t. z only
        distortion_grad = torch.autograd.grad(distortion, z, create_graph=True, retain_graph=True)[0]

        return distortion, distortion_grad
    
    def compute_complexity_field(self, z: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute complexity C(z) = ||∇_z decoder(z)||_F^2 and its gradient c_vec(z) = ∇_z C(z)

        Args:
            z: Latent code (requires_grad=True)

        Returns:
            complexity: Scalar complexity value (Frobenius norm squared of Jacobian)
            complexity_grad: Gradient of complexity w.r.t. z
        """
        if not z.requires_grad:
            z.requires_grad_(True)

        # Get decoder output
        x_recon = self.model.decode(z)

        # Compute Jacobian of decoder w.r.t. z more efficiently
        # Sum over output dimensions to get a scalar, then take gradient
        output_sum = torch.sum(x_recon)

        # First derivative (Jacobian)
        first_grad = torch.autograd.grad(output_sum, z, create_graph=True, retain_graph=True)[0]

        # Complexity is norm squared of the gradient
        complexity = torch.sum(first_grad ** 2)

        # Compute gradient of complexity w.r.t. z
        complexity_grad = torch.autograd.grad(complexity, z, create_graph=True, retain_graph=True)[0]

        return complexity, complexity_grad

class ExperimentRunner:
    """Main experiment runner for field alignment validation"""
    
    def __init__(self, latent_dim: int = 2, hidden_dims: List[int] = [256, 128], 
                 weight_decay: float = 1e-4, device: str = 'auto'):
        self.latent_dim = latent_dim
        self.hidden_dims = hidden_dims
        self.weight_decay = weight_decay
        
        # Set device
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Using device: {self.device}")
        
        # Initialize model
        self.model = SimpleAutoencoder(
            input_dim=784, 
            latent_dim=latent_dim, 
            hidden_dims=hidden_dims
        ).to(self.device)
        
        # Initialize field computer
        self.field_computer = FieldComputer(self.model, self.device)
        
        # Storage for results
        self.results = {
            'checkpoints': [],
            'field_data': [],
            'alignment_stats': [],
            'training_history': []
        }
    
    def load_data(self, data_dir: str = "MNIST", batch_size: int = 256) -> Tuple[DataLoader, DataLoader]:
        """Load and prepare MNIST data"""
        print("Loading MNIST dataset...")
        train_images, train_labels, test_images, test_labels = MNISTLoader.load_mnist(data_dir)
        
        # Flatten images
        train_images = train_images.reshape(-1, 784)
        test_images = test_images.reshape(-1, 784)
        
        # Convert to tensors
        train_dataset = TensorDataset(
            torch.FloatTensor(train_images),
            torch.LongTensor(train_labels)
        )
        test_dataset = TensorDataset(
            torch.FloatTensor(test_images),
            torch.LongTensor(test_labels)
        )
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        print(f"Train set: {len(train_dataset)} samples")
        print(f"Test set: {len(test_dataset)} samples")
        
        return train_loader, test_loader

    def train_model(self, train_loader: DataLoader, test_loader: DataLoader,
                    epochs: int = 100, lr: float = 1e-3, checkpoint_interval: int = 10) -> None:
        """Train autoencoder with checkpointing for field analysis"""
        print(f"Training autoencoder for {epochs} epochs...")

        # Setup optimizer with weight decay (L2 regularization)
        optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=self.weight_decay)
        criterion = nn.MSELoss()

        # Create checkpoints directory
        os.makedirs("checkpoints", exist_ok=True)

        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0

            for batch_idx, (data, _) in enumerate(train_loader):
                data = data.to(self.device)

                optimizer.zero_grad()
                recon, _ = self.model(data)
                loss = criterion(recon, data)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            # Validation phase
            self.model.eval()
            val_loss = 0.0
            with torch.no_grad():
                for data, _ in test_loader:
                    data = data.to(self.device)
                    recon, _ = self.model(data)
                    val_loss += criterion(recon, data).item()

            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(test_loader)

            # Store training history
            self.results['training_history'].append({
                'epoch': epoch,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss
            })

            if epoch % 10 == 0:
                print(f"Epoch {epoch:3d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}")

            # Save checkpoint and analyze fields
            if epoch % checkpoint_interval == 0:
                checkpoint_path = f"checkpoints/model_epoch_{epoch}.pth"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'val_loss': avg_val_loss
                }, checkpoint_path)

                self.results['checkpoints'].append(checkpoint_path)

                # Analyze fields at this checkpoint
                print(f"Analyzing fields at epoch {epoch}...")
                field_data = self.analyze_fields_at_checkpoint(test_loader, epoch)
                self.results['field_data'].append(field_data)

        print("Training completed!")

    def analyze_fields_at_checkpoint(self, test_loader: DataLoader, epoch: int,
                                   num_samples: int = 50) -> Dict:
        """Analyze distortion and complexity fields at a specific checkpoint"""
        self.model.eval()

        # Ensure field computer has the latest model state
        self.field_computer.model = self.model

        field_data = {
            'epoch': epoch,
            'latent_codes': [],
            'distortions': [],
            'complexities': [],
            'distortion_grads': [],
            'complexity_grads': [],
            'alignments': [],
            'distortion_norms': [],
            'complexity_norms': []
        }

        sample_count = 0
        with torch.no_grad():
            for data, _ in test_loader:
                if sample_count >= num_samples:
                    break

                data = data.to(self.device)
                batch_size = min(data.shape[0], num_samples - sample_count)
                data = data[:batch_size]

                for i in range(batch_size):
                    try:
                        x_original = data[i:i+1]  # Keep batch dimension

                        # Encode to get latent code
                        with torch.no_grad():
                            z_encoded = self.model.encode(x_original)

                        # Create a new tensor with gradient tracking
                        z = z_encoded.detach().clone()
                        z.requires_grad_(True)

                        # Debug: Check if z requires grad
                        if not z.requires_grad:
                            print(f"Warning: z does not require grad at sample {sample_count}")
                            continue

                        # Compute distortion field
                        distortion, distortion_grad = self.field_computer.compute_distortion_field(x_original, z)

                        # Compute complexity field
                        complexity, complexity_grad = self.field_computer.compute_complexity_field(z)
                    except Exception as e:
                        print(f"Error processing sample {sample_count}: {e}")
                        continue

                    # Compute alignment (dot product)
                    alignment = torch.dot(distortion_grad.flatten(), complexity_grad.flatten())

                    # Compute norms
                    distortion_norm = torch.norm(distortion_grad)
                    complexity_norm = torch.norm(complexity_grad)

                    # Store results
                    field_data['latent_codes'].append(z.detach().cpu().numpy())
                    field_data['distortions'].append(distortion.item())
                    field_data['complexities'].append(complexity.item())
                    field_data['distortion_grads'].append(distortion_grad.detach().cpu().numpy())
                    field_data['complexity_grads'].append(complexity_grad.detach().cpu().numpy())
                    field_data['alignments'].append(alignment.item())
                    field_data['distortion_norms'].append(distortion_norm.item())
                    field_data['complexity_norms'].append(complexity_norm.item())

                    sample_count += 1
                    if sample_count >= num_samples:
                        break

        # Convert lists to numpy arrays for easier analysis
        for key in ['latent_codes', 'distortion_grads', 'complexity_grads']:
            field_data[key] = np.array(field_data[key])

        for key in ['distortions', 'complexities', 'alignments', 'distortion_norms', 'complexity_norms']:
            field_data[key] = np.array(field_data[key])

        return field_data

    def compute_alignment_statistics(self, field_data: Dict) -> Dict:
        """Compute statistical measures of field alignment"""
        alignments = field_data['alignments']
        distortion_norms = field_data['distortion_norms']
        complexity_norms = field_data['complexity_norms']

        # Find high-magnitude regions (75th percentile)
        dist_threshold = np.percentile(distortion_norms, 75)
        comp_threshold = np.percentile(complexity_norms, 75)
        high_magnitude_mask = (distortion_norms >= dist_threshold) & (complexity_norms >= comp_threshold)

        stats = {
            'epoch': field_data['epoch'],
            'total_samples': len(alignments),
            'high_magnitude_samples': np.sum(high_magnitude_mask),
            'mean_alignment_all': np.mean(alignments),
            'mean_alignment_high_mag': np.mean(alignments[high_magnitude_mask]) if np.any(high_magnitude_mask) else 0,
            'std_alignment_all': np.std(alignments),
            'std_alignment_high_mag': np.std(alignments[high_magnitude_mask]) if np.any(high_magnitude_mask) else 0,
            'mean_distortion_norm': np.mean(distortion_norms),
            'mean_complexity_norm': np.mean(complexity_norms),
            'dist_threshold_75': dist_threshold,
            'comp_threshold_75': comp_threshold
        }

        # Compute correlations
        if len(alignments) > 1:
            # Pearson correlation between distortion norm and negative alignment
            neg_alignments = -alignments
            pearson_corr, pearson_p = pearsonr(distortion_norms, neg_alignments)
            spearman_corr, spearman_p = spearmanr(distortion_norms, neg_alignments)

            stats.update({
                'pearson_corr_dist_neg_align': pearson_corr,
                'pearson_p_value': pearson_p,
                'spearman_corr_dist_neg_align': spearman_corr,
                'spearman_p_value': spearman_p
            })

        return stats

    def analyze_all_checkpoints(self) -> None:
        """Compute alignment statistics for all collected field data"""
        print("Computing alignment statistics for all checkpoints...")

        for field_data in self.results['field_data']:
            stats = self.compute_alignment_statistics(field_data)
            self.results['alignment_stats'].append(stats)

        print(f"Computed statistics for {len(self.results['alignment_stats'])} checkpoints")

class Visualizer:
    """Visualization functions for field analysis"""

    def __init__(self, results_dir: str = "results"):
        self.results_dir = results_dir
        os.makedirs(results_dir, exist_ok=True)

        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

    def plot_training_history(self, training_history: List[Dict], save_path: str = None) -> None:
        """Plot training and validation loss over epochs"""
        epochs = [h['epoch'] for h in training_history]
        train_losses = [h['train_loss'] for h in training_history]
        val_losses = [h['val_loss'] for h in training_history]

        plt.figure(figsize=(10, 6))
        plt.plot(epochs, train_losses, label='Training Loss', linewidth=2)
        plt.plot(epochs, val_losses, label='Validation Loss', linewidth=2)
        plt.xlabel('Epoch')
        plt.ylabel('MSE Loss')
        plt.title('Autoencoder Training History')
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_field_vectors_2d(self, field_data: Dict, save_path: str = None) -> None:
        """Plot vector fields for 2D latent space"""
        if field_data['latent_codes'].shape[1] != 2:
            print("Vector field visualization only available for 2D latent space")
            return

        latent_codes = field_data['latent_codes']
        distortion_grads = field_data['distortion_grads']
        complexity_grads = field_data['complexity_grads']
        alignments = field_data['alignments']

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Distortion field
        axes[0, 0].quiver(latent_codes[:, 0], latent_codes[:, 1],
                         distortion_grads[:, 0], distortion_grads[:, 1],
                         alpha=0.7, scale=None)
        axes[0, 0].set_title(f'Distortion Field (Epoch {field_data["epoch"]})')
        axes[0, 0].set_xlabel('Latent Dim 1')
        axes[0, 0].set_ylabel('Latent Dim 2')

        # Complexity field
        axes[0, 1].quiver(latent_codes[:, 0], latent_codes[:, 1],
                         complexity_grads[:, 0], complexity_grads[:, 1],
                         alpha=0.7, scale=None, color='red')
        axes[0, 1].set_title(f'Complexity Field (Epoch {field_data["epoch"]})')
        axes[0, 1].set_xlabel('Latent Dim 1')
        axes[0, 1].set_ylabel('Latent Dim 2')

        # Alignment heatmap
        scatter = axes[1, 0].scatter(latent_codes[:, 0], latent_codes[:, 1],
                                   c=alignments, cmap='RdBu_r', alpha=0.7)
        axes[1, 0].set_title('Field Alignment (d⃗·c⃗)')
        axes[1, 0].set_xlabel('Latent Dim 1')
        axes[1, 0].set_ylabel('Latent Dim 2')
        plt.colorbar(scatter, ax=axes[1, 0])

        # Combined field visualization
        # Scale vectors for better visualization
        dist_scale = np.max(np.linalg.norm(distortion_grads, axis=1))
        comp_scale = np.max(np.linalg.norm(complexity_grads, axis=1))

        axes[1, 1].quiver(latent_codes[:, 0], latent_codes[:, 1],
                         distortion_grads[:, 0]/dist_scale, distortion_grads[:, 1]/dist_scale,
                         alpha=0.5, scale=None, color='blue', label='Distortion')
        axes[1, 1].quiver(latent_codes[:, 0], latent_codes[:, 1],
                         complexity_grads[:, 0]/comp_scale, complexity_grads[:, 1]/comp_scale,
                         alpha=0.5, scale=None, color='red', label='Complexity')
        axes[1, 1].set_title('Combined Fields (Normalized)')
        axes[1, 1].set_xlabel('Latent Dim 1')
        axes[1, 1].set_ylabel('Latent Dim 2')
        axes[1, 1].legend()

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_alignment_evolution(self, alignment_stats: List[Dict], save_path: str = None) -> None:
        """Plot evolution of field alignment over training"""
        epochs = [s['epoch'] for s in alignment_stats]
        mean_align_all = [s['mean_alignment_all'] for s in alignment_stats]
        mean_align_high = [s['mean_alignment_high_mag'] for s in alignment_stats]
        pearson_corrs = [s.get('pearson_corr_dist_neg_align', 0) for s in alignment_stats]

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Mean alignment over time
        axes[0, 0].plot(epochs, mean_align_all, 'o-', label='All Regions', linewidth=2)
        axes[0, 0].plot(epochs, mean_align_high, 's-', label='High Magnitude Regions', linewidth=2)
        axes[0, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Mean Alignment (d⃗·c⃗)')
        axes[0, 0].set_title('Field Alignment Evolution')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Correlation evolution
        axes[0, 1].plot(epochs, pearson_corrs, 'o-', color='green', linewidth=2)
        axes[0, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Pearson Correlation')
        axes[0, 1].set_title('Distortion vs Negative Alignment Correlation')
        axes[0, 1].grid(True, alpha=0.3)

        # Field magnitude evolution
        mean_dist_norms = [s['mean_distortion_norm'] for s in alignment_stats]
        mean_comp_norms = [s['mean_complexity_norm'] for s in alignment_stats]

        axes[1, 0].plot(epochs, mean_dist_norms, 'o-', label='Distortion Field', linewidth=2)
        axes[1, 0].plot(epochs, mean_comp_norms, 's-', label='Complexity Field', linewidth=2)
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Mean Field Magnitude')
        axes[1, 0].set_title('Field Magnitude Evolution')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # High magnitude sample count
        high_mag_counts = [s['high_magnitude_samples'] for s in alignment_stats]
        axes[1, 1].plot(epochs, high_mag_counts, 'o-', color='purple', linewidth=2)
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('High Magnitude Sample Count')
        axes[1, 1].set_title('High Magnitude Regions Over Time')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

class Reporter:
    """Generate comprehensive reports of field alignment analysis"""

    def __init__(self, results_dir: str = "results"):
        self.results_dir = results_dir
        os.makedirs(results_dir, exist_ok=True)

    def generate_summary_table(self, alignment_stats: List[Dict]) -> pd.DataFrame:
        """Generate summary statistics table"""
        df = pd.DataFrame(alignment_stats)

        # Select key columns for summary
        summary_cols = [
            'epoch', 'total_samples', 'high_magnitude_samples',
            'mean_alignment_all', 'mean_alignment_high_mag',
            'pearson_corr_dist_neg_align', 'spearman_corr_dist_neg_align',
            'mean_distortion_norm', 'mean_complexity_norm'
        ]

        summary_df = df[summary_cols].round(6)
        return summary_df

    def save_results(self, results: Dict, filename: str = "field_alignment_results.pkl") -> None:
        """Save complete results to pickle file"""
        filepath = os.path.join(self.results_dir, filename)
        with open(filepath, 'wb') as f:
            pickle.dump(results, f)
        print(f"Results saved to {filepath}")

    def generate_markdown_report(self, results: Dict, alignment_stats: List[Dict]) -> str:
        """Generate comprehensive markdown report"""

        # Get final statistics
        final_stats = alignment_stats[-1] if alignment_stats else {}
        initial_stats = alignment_stats[0] if alignment_stats else {}

        report = f"""# Field Alignment Validation Report

## Experiment Overview

This experiment tested whether distortion and complexity vector fields in the latent space of an autoencoder exhibit the predicted anti-alignment ("optimal opposition") characteristic of compression-efficient learning.

### Model Configuration
- **Latent Dimension**: {results.get('latent_dim', 'N/A')}
- **Architecture**: Encoder/Decoder with hidden layers
- **Regularization**: L2 weight decay
- **Dataset**: MNIST (28×28 grayscale, normalized to [0,1])

### Training Summary
- **Total Epochs**: {len(results.get('training_history', []))}
- **Checkpoints Analyzed**: {len(alignment_stats)}
- **Final Training Loss**: {results['training_history'][-1]['train_loss']:.6f if results.get('training_history') else 'N/A'}
- **Final Validation Loss**: {results['training_history'][-1]['val_loss']:.6f if results.get('training_history') else 'N/A'}

## Field Alignment Analysis

### Key Findings

#### Initial State (Epoch {initial_stats.get('epoch', 0)})
- **Mean Alignment (All Regions)**: {initial_stats.get('mean_alignment_all', 0):.6f}
- **Mean Alignment (High Magnitude)**: {initial_stats.get('mean_alignment_high_mag', 0):.6f}
- **Pearson Correlation (Distortion vs -Alignment)**: {initial_stats.get('pearson_corr_dist_neg_align', 0):.6f}

#### Final State (Epoch {final_stats.get('epoch', 0)})
- **Mean Alignment (All Regions)**: {final_stats.get('mean_alignment_all', 0):.6f}
- **Mean Alignment (High Magnitude)**: {final_stats.get('mean_alignment_high_mag', 0):.6f}
- **Pearson Correlation (Distortion vs -Alignment)**: {final_stats.get('pearson_corr_dist_neg_align', 0):.6f}

### Theoretical Predictions vs Observations

**Hypothesis**: In well-regularized autoencoders, distortion and complexity fields should exhibit anti-alignment (negative dot product) in high-magnitude regions, indicating optimal compression trade-offs.

**Observations**:
"""

        # Analyze alignment trend
        if len(alignment_stats) > 1:
            final_align = final_stats.get('mean_alignment_high_mag', 0)
            initial_align = initial_stats.get('mean_alignment_high_mag', 0)

            if final_align < initial_align and final_align < 0:
                report += "\n✅ **SUPPORTS THEORY**: Field alignment became more negative over training, indicating emergence of anti-alignment in high-magnitude regions.\n"
            elif final_align < 0:
                report += "\n⚠️ **PARTIAL SUPPORT**: Fields show anti-alignment but trend is unclear.\n"
            else:
                report += "\n❌ **CONTRADICTS THEORY**: Fields do not show expected anti-alignment pattern.\n"

        report += f"""
### Statistical Summary

The following table shows the evolution of key metrics across training checkpoints:

{self.generate_summary_table(alignment_stats).to_markdown(index=False)}

### Interpretation

1. **Field Opposition**: {'Strong' if final_stats.get('mean_alignment_high_mag', 0) < -0.1 else 'Weak' if final_stats.get('mean_alignment_high_mag', 0) < 0 else 'None'} anti-alignment observed in high-magnitude regions.

2. **Correlation Analysis**: Pearson correlation of {final_stats.get('pearson_corr_dist_neg_align', 0):.3f} between distortion magnitude and negative alignment.

3. **Learning Dynamics**: Field magnitudes {'decreased' if final_stats.get('mean_distortion_norm', 1) < initial_stats.get('mean_distortion_norm', 1) else 'increased'} over training, suggesting {'convergence' if final_stats.get('mean_distortion_norm', 1) < initial_stats.get('mean_distortion_norm', 1) else 'continued learning'}.

### Conclusions

This experiment provides {'strong' if final_stats.get('mean_alignment_high_mag', 0) < -0.1 else 'limited'} evidence for the field opposition theory in autoencoder learning. The results suggest that {'well-regularized autoencoders do exhibit the predicted anti-alignment between distortion and complexity fields' if final_stats.get('mean_alignment_high_mag', 0) < -0.1 else 'the relationship between distortion and complexity fields is more complex than predicted'}.

### Files Generated
- Field vector visualizations
- Alignment evolution plots
- Statistical summary tables
- Complete results pickle file

---
*Generated by Field Alignment Validation Framework*
"""

        return report

def run_complete_experiment(latent_dim: int = 2, epochs: int = 50, checkpoint_interval: int = 10):
    """Run the complete field alignment validation experiment"""

    print("="*80)
    print("FIELD ALIGNMENT VALIDATION FRAMEWORK")
    print("Testing Field Opposition Theory in Autoencoder Learning")
    print("="*80)

    # Initialize experiment
    experiment = ExperimentRunner(latent_dim=latent_dim, hidden_dims=[256, 128])

    # Load data
    train_loader, test_loader = experiment.load_data()

    print(f"\nModel Configuration:")
    print(f"- Latent dimension: {latent_dim}")
    print(f"- Architecture: {experiment.model}")
    print(f"- Device: {experiment.device}")
    print(f"- Training epochs: {epochs}")
    print(f"- Checkpoint interval: {checkpoint_interval}")

    # Train model with field analysis
    print(f"\n{'='*50}")
    print("PHASE 1: TRAINING WITH FIELD ANALYSIS")
    print(f"{'='*50}")

    experiment.train_model(train_loader, test_loader, epochs=epochs,
                          checkpoint_interval=checkpoint_interval)

    # Analyze all checkpoints
    print(f"\n{'='*50}")
    print("PHASE 2: STATISTICAL ANALYSIS")
    print(f"{'='*50}")

    experiment.analyze_all_checkpoints()

    # Generate visualizations
    print(f"\n{'='*50}")
    print("PHASE 3: VISUALIZATION AND REPORTING")
    print(f"{'='*50}")

    visualizer = Visualizer()
    reporter = Reporter()

    # Plot training history
    print("Generating training history plot...")
    visualizer.plot_training_history(
        experiment.results['training_history'],
        save_path="results/training_history.png"
    )

    # Plot field evolution
    print("Generating alignment evolution plot...")
    visualizer.plot_alignment_evolution(
        experiment.results['alignment_stats'],
        save_path="results/alignment_evolution.png"
    )

    # Plot field vectors for final checkpoint (if 2D)
    if latent_dim == 2 and experiment.results['field_data']:
        print("Generating field vector plots...")
        final_field_data = experiment.results['field_data'][-1]
        visualizer.plot_field_vectors_2d(
            final_field_data,
            save_path="results/field_vectors_final.png"
        )

        # Also plot initial checkpoint for comparison
        if len(experiment.results['field_data']) > 1:
            initial_field_data = experiment.results['field_data'][0]
            visualizer.plot_field_vectors_2d(
                initial_field_data,
                save_path="results/field_vectors_initial.png"
            )

    # Generate and save report
    print("Generating comprehensive report...")
    report_text = reporter.generate_markdown_report(
        experiment.results,
        experiment.results['alignment_stats']
    )

    # Save report
    with open("results/field_alignment_report.md", "w") as f:
        f.write(report_text)

    # Save complete results
    reporter.save_results(experiment.results)

    # Print summary
    print(f"\n{'='*50}")
    print("EXPERIMENT COMPLETED SUCCESSFULLY!")
    print(f"{'='*50}")

    if experiment.results['alignment_stats']:
        final_stats = experiment.results['alignment_stats'][-1]
        print(f"\nFinal Results Summary:")
        print(f"- Mean alignment (high magnitude): {final_stats.get('mean_alignment_high_mag', 0):.6f}")
        print(f"- Pearson correlation: {final_stats.get('pearson_corr_dist_neg_align', 0):.6f}")
        print(f"- High magnitude samples: {final_stats.get('high_magnitude_samples', 0)}")

        # Theoretical assessment
        final_align = final_stats.get('mean_alignment_high_mag', 0)
        if final_align < -0.1:
            print(f"\n✅ STRONG SUPPORT for field opposition theory!")
        elif final_align < 0:
            print(f"\n⚠️ WEAK SUPPORT for field opposition theory.")
        else:
            print(f"\n❌ NO SUPPORT for field opposition theory.")

    print(f"\nAll results saved to 'results/' directory:")
    print(f"- field_alignment_report.md (comprehensive report)")
    print(f"- field_alignment_results.pkl (complete data)")
    print(f"- *.png (visualization plots)")

    return experiment.results

if __name__ == "__main__":
    # Run the complete experiment with reduced parameters for testing
    results = run_complete_experiment(
        latent_dim=2,      # Use 2D for visualization
        epochs=20,         # Reduced for faster testing
        checkpoint_interval=5  # More frequent checkpoints for testing
    )
