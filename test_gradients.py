"""
Test script to debug gradient computation issues
"""

import torch
import torch.nn as nn
import numpy as np

# Set random seed
torch.manual_seed(42)

class SimpleAutoencoder(nn.Module):
    def __init__(self, input_dim=784, latent_dim=2):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, latent_dim)
        )
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, input_dim),
            nn.Sigmoid()
        )
    
    def encode(self, x):
        return self.encoder(x)
    
    def decode(self, z):
        return self.decoder(z)

def test_gradient_computation():
    print("Testing gradient computation...")
    
    # Create model
    model = SimpleAutoencoder()
    model.eval()
    
    # Create dummy data
    x_original = torch.randn(1, 784)
    
    # Encode
    with torch.no_grad():
        z_encoded = model.encode(x_original)
    
    # Create z with gradients
    z = z_encoded.detach().clone()
    z.requires_grad_(True)
    
    print(f"z.requires_grad: {z.requires_grad}")
    print(f"z.grad_fn: {z.grad_fn}")
    
    # Test distortion computation
    try:
        x_recon = model.decode(z)
        print(f"x_recon.requires_grad: {x_recon.requires_grad}")
        print(f"x_recon.grad_fn: {x_recon.grad_fn}")
        
        x_orig_detached = x_original.detach()
        distortion = torch.sum((x_orig_detached - x_recon) ** 2)
        print(f"distortion.requires_grad: {distortion.requires_grad}")
        print(f"distortion.grad_fn: {distortion.grad_fn}")
        
        # Compute gradient
        distortion_grad = torch.autograd.grad(distortion, z, create_graph=True, retain_graph=True)[0]
        print(f"Distortion gradient computed successfully: {distortion_grad.shape}")
        
    except Exception as e:
        print(f"Error in distortion computation: {e}")
        return False
    
    # Test complexity computation
    try:
        # Reset z
        z = z_encoded.detach().clone()
        z.requires_grad_(True)
        
        x_recon = model.decode(z)
        output_sum = torch.sum(x_recon)
        
        first_grad = torch.autograd.grad(output_sum, z, create_graph=True, retain_graph=True)[0]
        complexity = torch.sum(first_grad ** 2)
        complexity_grad = torch.autograd.grad(complexity, z, create_graph=True, retain_graph=True)[0]
        
        print(f"Complexity gradient computed successfully: {complexity_grad.shape}")
        
    except Exception as e:
        print(f"Error in complexity computation: {e}")
        return False
    
    print("All gradient computations successful!")
    return True

if __name__ == "__main__":
    success = test_gradient_computation()
    if success:
        print("✅ Gradient computation test passed!")
    else:
        print("❌ Gradient computation test failed!")
