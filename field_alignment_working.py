"""
Working Field Alignment Validation Framework
Based on successful minimal test
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import pandas as pd
import os
from scipy.stats import pearsonr, spearmanr
import pickle

# Set random seeds
torch.manual_seed(42)
np.random.seed(42)

class MNISTLoader:
    @staticmethod
    def load_idx_images(filename: str) -> np.ndarray:
        with open(filename, 'rb') as f:
            magic = int.from_bytes(f.read(4), 'big')
            num_images = int.from_bytes(f.read(4), 'big')
            rows = int.from_bytes(f.read(4), 'big')
            cols = int.from_bytes(f.read(4), 'big')
            images = np.frombuffer(f.read(), dtype=np.uint8)
            images = images.reshape(num_images, rows, cols)
        return images.astype(np.float32) / 255.0

    @staticmethod
    def load_idx_labels(filename: str) -> np.ndarray:
        with open(filename, 'rb') as f:
            magic = int.from_bytes(f.read(4), 'big')
            num_labels = int.from_bytes(f.read(4), 'big')
            labels = np.frombuffer(f.read(), dtype=np.uint8)
        return labels

    @classmethod
    def load_mnist(cls, data_dir: str = "MNIST"):
        train_images = cls.load_idx_images(f"{data_dir}/train-images.idx3-ubyte")
        train_labels = cls.load_idx_labels(f"{data_dir}/train-labels.idx1-ubyte")
        test_images = cls.load_idx_images(f"{data_dir}/t10k-images.idx3-ubyte")
        test_labels = cls.load_idx_labels(f"{data_dir}/t10k-labels.idx1-ubyte")
        return train_images, train_labels, test_images, test_labels

class SimpleAutoencoder(nn.Module):
    def __init__(self, input_dim=784, latent_dim=2, hidden_dims=[256, 128]):
        super().__init__()
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        encoder_layers.append(nn.Linear(prev_dim, latent_dim))
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Decoder
        decoder_layers = []
        prev_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU()
            ])
            prev_dim = hidden_dim
        decoder_layers.extend([
            nn.Linear(prev_dim, input_dim),
            nn.Sigmoid()
        ])
        self.decoder = nn.Sequential(*decoder_layers)
    
    def encode(self, x):
        return self.encoder(x)
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        z = self.encode(x)
        x_recon = self.decode(z)
        return x_recon, z

def compute_fields(model, x_original, z):
    """Compute distortion and complexity fields"""
    model.eval()
    
    # Ensure z requires gradients
    if not z.requires_grad:
        z.requires_grad_(True)
    
    # Distortion field: D(z) = ||x - x_hat||^2
    x_recon = model.decode(z)
    x_orig_detached = x_original.detach()
    distortion = torch.sum((x_orig_detached - x_recon) ** 2)
    distortion_grad = torch.autograd.grad(distortion, z, create_graph=True, retain_graph=True)[0]
    
    # Complexity field: C(z) = ||∇_z decoder(z)||_F^2 (simplified)
    output_sum = torch.sum(x_recon)
    first_grad = torch.autograd.grad(output_sum, z, create_graph=True, retain_graph=True)[0]
    complexity = torch.sum(first_grad ** 2)
    complexity_grad = torch.autograd.grad(complexity, z, create_graph=True, retain_graph=True)[0]
    
    return distortion, distortion_grad, complexity, complexity_grad

def analyze_fields_at_epoch(model, test_loader, epoch, num_samples=200):
    """Analyze fields at a specific epoch"""
    print(f"Analyzing fields at epoch {epoch}...")
    
    model.eval()
    field_data = {
        'epoch': epoch,
        'latent_codes': [],
        'distortions': [],
        'complexities': [],
        'distortion_grads': [],
        'complexity_grads': [],
        'alignments': [],
        'distortion_norms': [],
        'complexity_norms': []
    }
    
    sample_count = 0
    for data, _ in test_loader:
        if sample_count >= num_samples:
            break
        
        batch_size = min(data.shape[0], num_samples - sample_count)
        data = data[:batch_size]
        
        for i in range(batch_size):
            try:
                x_original = data[i:i+1]
                
                # Encode
                with torch.no_grad():
                    z_encoded = model.encode(x_original)
                
                # Create z with gradients
                z = z_encoded.detach().clone()
                z.requires_grad_(True)
                
                # Compute fields
                distortion, distortion_grad, complexity, complexity_grad = compute_fields(model, x_original, z)
                
                # Compute alignment and norms
                alignment = torch.dot(distortion_grad.flatten(), complexity_grad.flatten())
                distortion_norm = torch.norm(distortion_grad)
                complexity_norm = torch.norm(complexity_grad)
                
                # Store results (flatten to remove batch dimension)
                field_data['latent_codes'].append(z.detach().cpu().numpy().flatten())
                field_data['distortions'].append(distortion.item())
                field_data['complexities'].append(complexity.item())
                field_data['distortion_grads'].append(distortion_grad.detach().cpu().numpy().flatten())
                field_data['complexity_grads'].append(complexity_grad.detach().cpu().numpy().flatten())
                field_data['alignments'].append(alignment.item())
                field_data['distortion_norms'].append(distortion_norm.item())
                field_data['complexity_norms'].append(complexity_norm.item())
                
                sample_count += 1
                if sample_count >= num_samples:
                    break
                    
            except Exception as e:
                print(f"Error processing sample {sample_count}: {e}")
                continue
    
    # Convert to numpy arrays
    for key in ['latent_codes', 'distortion_grads', 'complexity_grads']:
        field_data[key] = np.array(field_data[key])
    
    for key in ['distortions', 'complexities', 'alignments', 'distortion_norms', 'complexity_norms']:
        field_data[key] = np.array(field_data[key])
    
    print(f"Analyzed {sample_count} samples")
    return field_data

def compute_alignment_statistics(field_data):
    """Compute statistical measures of field alignment"""
    alignments = field_data['alignments']
    distortion_norms = field_data['distortion_norms']
    complexity_norms = field_data['complexity_norms']
    
    # Find high-magnitude regions (75th percentile)
    dist_threshold = np.percentile(distortion_norms, 75)
    comp_threshold = np.percentile(complexity_norms, 75)
    high_magnitude_mask = (distortion_norms >= dist_threshold) & (complexity_norms >= comp_threshold)
    
    stats = {
        'epoch': field_data['epoch'],
        'total_samples': len(alignments),
        'high_magnitude_samples': np.sum(high_magnitude_mask),
        'mean_alignment_all': np.mean(alignments),
        'mean_alignment_high_mag': np.mean(alignments[high_magnitude_mask]) if np.any(high_magnitude_mask) else 0,
        'std_alignment_all': np.std(alignments),
        'std_alignment_high_mag': np.std(alignments[high_magnitude_mask]) if np.any(high_magnitude_mask) else 0,
        'mean_distortion_norm': np.mean(distortion_norms),
        'mean_complexity_norm': np.mean(complexity_norms),
        'dist_threshold_75': dist_threshold,
        'comp_threshold_75': comp_threshold
    }
    
    # Compute correlations
    if len(alignments) > 1:
        neg_alignments = -alignments
        pearson_corr, pearson_p = pearsonr(distortion_norms, neg_alignments)
        spearman_corr, spearman_p = spearmanr(distortion_norms, neg_alignments)
        
        stats.update({
            'pearson_corr_dist_neg_align': pearson_corr,
            'pearson_p_value': pearson_p,
            'spearman_corr_dist_neg_align': spearman_corr,
            'spearman_p_value': spearman_p
        })
    
    return stats

def main():
    print("="*80)
    print("FIELD ALIGNMENT VALIDATION FRAMEWORK")
    print("Testing Field Opposition Theory in Autoencoder Learning")
    print("="*80)
    
    # Create results directory
    os.makedirs("results", exist_ok=True)
    
    # Load data
    print("Loading MNIST...")
    train_images, train_labels, test_images, test_labels = MNISTLoader.load_mnist()
    
    # Prepare data
    train_images = train_images.reshape(-1, 784)
    test_images = test_images.reshape(-1, 784)
    
    train_dataset = TensorDataset(torch.FloatTensor(train_images.copy()), torch.LongTensor(train_labels))
    test_dataset = TensorDataset(torch.FloatTensor(test_images.copy()), torch.LongTensor(test_labels))
    
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=256, shuffle=False)
    
    print(f"Train: {len(train_dataset)}, Test: {len(test_dataset)}")
    
    # Create model
    model = SimpleAutoencoder(latent_dim=2, hidden_dims=[256, 128])
    optimizer = optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-4)
    criterion = nn.MSELoss()
    
    print(f"Model: {model}")
    
    # Training and analysis
    epochs = 30
    checkpoint_interval = 5
    
    training_history = []
    field_data_history = []
    alignment_stats_history = []
    
    print(f"\nTraining for {epochs} epochs with field analysis every {checkpoint_interval} epochs...")
    
    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        
        for data, _ in train_loader:
            optimizer.zero_grad()
            recon, _ = model(data)
            loss = criterion(recon, data)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # Validation
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for data, _ in test_loader:
                recon, _ = model(data)
                val_loss += criterion(recon, data).item()
        
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(test_loader)
        
        training_history.append({
            'epoch': epoch,
            'train_loss': avg_train_loss,
            'val_loss': avg_val_loss
        })
        
        if epoch % 5 == 0:
            print(f"Epoch {epoch:3d}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}")
        
        # Field analysis at checkpoints
        if epoch % checkpoint_interval == 0:
            field_data = analyze_fields_at_epoch(model, test_loader, epoch)
            field_data_history.append(field_data)
            
            stats = compute_alignment_statistics(field_data)
            alignment_stats_history.append(stats)
            
            print(f"  Mean alignment (all): {stats['mean_alignment_all']:.6f}")
            print(f"  Mean alignment (high mag): {stats['mean_alignment_high_mag']:.6f}")
    
    print("\nTraining completed!")
    return model, training_history, field_data_history, alignment_stats_history

def create_visualizations(training_history, field_data_history, alignment_stats_history):
    """Create comprehensive visualizations"""
    print("\nGenerating visualizations...")

    # 1. Training history
    epochs = [h['epoch'] for h in training_history]
    train_losses = [h['train_loss'] for h in training_history]
    val_losses = [h['val_loss'] for h in training_history]

    plt.figure(figsize=(12, 8))

    plt.subplot(2, 2, 1)
    plt.plot(epochs, train_losses, label='Training Loss', linewidth=2)
    plt.plot(epochs, val_losses, label='Validation Loss', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.title('Training History')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Alignment evolution
    checkpoint_epochs = [s['epoch'] for s in alignment_stats_history]
    mean_align_all = [s['mean_alignment_all'] for s in alignment_stats_history]
    mean_align_high = [s['mean_alignment_high_mag'] for s in alignment_stats_history]

    plt.subplot(2, 2, 2)
    plt.plot(checkpoint_epochs, mean_align_all, 'o-', label='All Regions', linewidth=2)
    plt.plot(checkpoint_epochs, mean_align_high, 's-', label='High Magnitude Regions', linewidth=2)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    plt.xlabel('Epoch')
    plt.ylabel('Mean Alignment (d⃗·c⃗)')
    plt.title('Field Alignment Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. Field magnitudes
    mean_dist_norms = [s['mean_distortion_norm'] for s in alignment_stats_history]
    mean_comp_norms = [s['mean_complexity_norm'] for s in alignment_stats_history]

    plt.subplot(2, 2, 3)
    plt.plot(checkpoint_epochs, mean_dist_norms, 'o-', label='Distortion Field', linewidth=2)
    plt.plot(checkpoint_epochs, mean_comp_norms, 's-', label='Complexity Field', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Mean Field Magnitude')
    plt.title('Field Magnitude Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Vector field visualization (final epoch)
    if field_data_history:
        final_field_data = field_data_history[-1]
        latent_codes = final_field_data['latent_codes']
        alignments = final_field_data['alignments']

        plt.subplot(2, 2, 4)
        scatter = plt.scatter(latent_codes[:, 0], latent_codes[:, 1],
                            c=alignments, cmap='RdBu_r', alpha=0.7)
        plt.colorbar(scatter)
        plt.xlabel('Latent Dim 1')
        plt.ylabel('Latent Dim 2')
        plt.title(f'Field Alignment (Epoch {final_field_data["epoch"]})')

    plt.tight_layout()
    plt.savefig('results/field_alignment_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Detailed vector field plot for final epoch
    if field_data_history:
        final_field_data = field_data_history[-1]
        latent_codes = final_field_data['latent_codes']
        distortion_grads = final_field_data['distortion_grads']
        complexity_grads = final_field_data['complexity_grads']
        alignments = final_field_data['alignments']

        plt.figure(figsize=(15, 12))

        # Distortion field
        plt.subplot(2, 2, 1)
        plt.quiver(latent_codes[:, 0], latent_codes[:, 1],
                  distortion_grads[:, 0], distortion_grads[:, 1],
                  alpha=0.7, scale=None)
        plt.title(f'Distortion Field (Epoch {final_field_data["epoch"]})')
        plt.xlabel('Latent Dim 1')
        plt.ylabel('Latent Dim 2')

        # Complexity field
        plt.subplot(2, 2, 2)
        plt.quiver(latent_codes[:, 0], latent_codes[:, 1],
                  complexity_grads[:, 0], complexity_grads[:, 1],
                  alpha=0.7, scale=None, color='red')
        plt.title(f'Complexity Field (Epoch {final_field_data["epoch"]})')
        plt.xlabel('Latent Dim 1')
        plt.ylabel('Latent Dim 2')

        # Alignment heatmap
        plt.subplot(2, 2, 3)
        scatter = plt.scatter(latent_codes[:, 0], latent_codes[:, 1],
                            c=alignments, cmap='RdBu_r', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('Field Alignment (d⃗·c⃗)')
        plt.xlabel('Latent Dim 1')
        plt.ylabel('Latent Dim 2')

        # Combined fields
        plt.subplot(2, 2, 4)
        # Normalize for visualization
        dist_scale = np.max(np.linalg.norm(distortion_grads, axis=1))
        comp_scale = np.max(np.linalg.norm(complexity_grads, axis=1))

        plt.quiver(latent_codes[:, 0], latent_codes[:, 1],
                  distortion_grads[:, 0]/dist_scale, distortion_grads[:, 1]/dist_scale,
                  alpha=0.5, scale=None, color='blue', label='Distortion')
        plt.quiver(latent_codes[:, 0], latent_codes[:, 1],
                  complexity_grads[:, 0]/comp_scale, complexity_grads[:, 1]/comp_scale,
                  alpha=0.5, scale=None, color='red', label='Complexity')
        plt.title('Combined Fields (Normalized)')
        plt.xlabel('Latent Dim 1')
        plt.ylabel('Latent Dim 2')
        plt.legend()

        plt.tight_layout()
        plt.savefig('results/vector_fields_final.png', dpi=300, bbox_inches='tight')
        plt.show()

def generate_report(training_history, field_data_history, alignment_stats_history):
    """Generate comprehensive markdown report"""
    print("\nGenerating report...")

    # Create summary table
    df = pd.DataFrame(alignment_stats_history)

    # Get key statistics
    initial_stats = alignment_stats_history[0] if alignment_stats_history else {}
    final_stats = alignment_stats_history[-1] if alignment_stats_history else {}

    report = f"""# Field Alignment Validation Report

## Experiment Overview

This experiment tested whether distortion and complexity vector fields in the latent space of an autoencoder exhibit the predicted anti-alignment ("optimal opposition") characteristic of compression-efficient learning.

### Model Configuration
- **Latent Dimension**: 2D (for visualization)
- **Architecture**: Encoder/Decoder with hidden layers [256, 128]
- **Regularization**: L2 weight decay (1e-4)
- **Dataset**: MNIST (28×28 grayscale, normalized to [0,1])

### Training Summary
- **Total Epochs**: {len(training_history)}
- **Checkpoints Analyzed**: {len(alignment_stats_history)}
- **Final Training Loss**: {training_history[-1]['train_loss']:.6f}
- **Final Validation Loss**: {training_history[-1]['val_loss']:.6f}

## Field Alignment Analysis

### Key Findings

#### Initial State (Epoch {initial_stats.get('epoch', 0)})
- **Mean Alignment (All Regions)**: {initial_stats.get('mean_alignment_all', 0):.6f}
- **Mean Alignment (High Magnitude)**: {initial_stats.get('mean_alignment_high_mag', 0):.6f}

#### Final State (Epoch {final_stats.get('epoch', 0)})
- **Mean Alignment (All Regions)**: {final_stats.get('mean_alignment_all', 0):.6f}
- **Mean Alignment (High Magnitude)**: {final_stats.get('mean_alignment_high_mag', 0):.6f}

### Theoretical Predictions vs Observations

**Hypothesis**: In well-regularized autoencoders, distortion and complexity fields should exhibit anti-alignment (negative dot product) in high-magnitude regions, indicating optimal compression trade-offs.

**Observations**:
"""

    # Analyze alignment trend
    final_align = final_stats.get('mean_alignment_high_mag', 0)
    initial_align = initial_stats.get('mean_alignment_high_mag', 0)

    if final_align < 0:
        report += "\n✅ **SUPPORTS THEORY**: Fields show anti-alignment in high-magnitude regions.\n"
    elif final_align < initial_align:
        report += "\n⚠️ **PARTIAL SUPPORT**: Alignment decreased over training but remains positive.\n"
    else:
        report += "\n❌ **CONTRADICTS THEORY**: Fields show positive alignment, not the expected opposition.\n"

    report += f"""
### Statistical Summary

{df.round(6).to_markdown(index=False)}

### Interpretation

1. **Field Opposition**: {'Strong' if final_align < -0.1 else 'Weak' if final_align < 0 else 'None'} anti-alignment observed in high-magnitude regions.

2. **Learning Dynamics**: Alignment {'decreased' if final_align < initial_align else 'increased'} from {initial_align:.3f} to {final_align:.3f} over training.

3. **Field Evolution**: Both distortion and complexity field magnitudes evolved during training, suggesting active learning dynamics.

### Conclusions

This experiment provides {'strong' if final_align < -0.1 else 'limited'} evidence for the field opposition theory in autoencoder learning.

**Key Insights**:
- The autoencoder shows {'convergent' if final_align < initial_align else 'divergent'} field alignment behavior
- High-magnitude regions {'do' if final_align < 0 else 'do not'} exhibit the predicted anti-alignment
- The regularization and architecture {'successfully' if final_align < 0 else 'did not'} produce the theoretical field opposition

### Future Work
- Test with stronger regularization
- Experiment with different latent dimensions
- Analyze field behavior in deeper networks
- Compare with VAE architectures

---
*Generated by Field Alignment Validation Framework*
"""

    # Save report
    with open("results/field_alignment_report.md", "w") as f:
        f.write(report)

    # Save data
    results = {
        'training_history': training_history,
        'field_data_history': field_data_history,
        'alignment_stats_history': alignment_stats_history
    }

    with open("results/field_alignment_results.pkl", "wb") as f:
        pickle.dump(results, f)

    print("Report and data saved to results/ directory")

    return report

if __name__ == "__main__":
    model, training_history, field_data_history, alignment_stats_history = main()

    # Generate visualizations and report
    create_visualizations(training_history, field_data_history, alignment_stats_history)
    report = generate_report(training_history, field_data_history, alignment_stats_history)

    print("\n" + "="*80)
    print("EXPERIMENT COMPLETED SUCCESSFULLY!")
    print("="*80)

    if alignment_stats_history:
        final_stats = alignment_stats_history[-1]
        print(f"\nFinal Results Summary:")
        print(f"- Mean alignment (high magnitude): {final_stats.get('mean_alignment_high_mag', 0):.6f}")
        print(f"- High magnitude samples: {final_stats.get('high_magnitude_samples', 0)}")

        # Theoretical assessment
        final_align = final_stats.get('mean_alignment_high_mag', 0)
        if final_align < -0.1:
            print(f"\n✅ STRONG SUPPORT for field opposition theory!")
        elif final_align < 0:
            print(f"\n⚠️ WEAK SUPPORT for field opposition theory.")
        else:
            print(f"\n❌ NO SUPPORT for field opposition theory.")

    print(f"\nAll results saved to 'results/' directory:")
    print(f"- field_alignment_report.md (comprehensive report)")
    print(f"- field_alignment_results.pkl (complete data)")
    print(f"- *.png (visualization plots)")
